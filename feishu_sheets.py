"""
飞书表格数据获取模块
用于从飞书表格中获取数据
"""

import requests
import json
import re
import os
from urllib.parse import urlparse, parse_qs
from feishu_auth import FeishuAuth
from dotenv import load_dotenv
from datetime import datetime, timedelta

# 加载环境变量
load_dotenv()

class FeishuSheets:
    def __init__(self):
        self.auth = FeishuAuth()
        self.sheet_urls = os.getenv('SHEET_URLS', '').split(',')

    def convert_excel_date(self, excel_date):
        """
        将Excel日期序列号转换为日期字符串
        Excel日期从1900年1月1日开始计算
        """
        try:
            # 尝试将字符串转换为数字
            if isinstance(excel_date, str):
                # 如果已经是日期格式，直接返回
                if '/' in excel_date or '-' in excel_date:
                    return excel_date
                # 尝试转换为浮点数
                excel_date = float(excel_date)

            if isinstance(excel_date, (int, float)):
                # Excel日期基准：1900年1月1日（但Excel错误地认为1900年是闰年）
                # 所以我们使用1899年12月30日作为基准
                base_date = datetime(1899, 12, 30)
                converted_date = base_date + timedelta(days=excel_date)
                return converted_date.strftime('%Y-%m-%d')

            # 如果无法转换，返回原值
            return str(excel_date)

        except (ValueError, TypeError):
            # 如果转换失败，返回原值
            return str(excel_date)
        
    def parse_sheet_url(self, url):
        """
        解析飞书表格URL，提取spreadsheet_token和sheet_id
        """
        try:
            # 从URL中提取spreadsheet_token
            # URL格式: https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=YPHiE4
            path_parts = urlparse(url).path.split('/')
            spreadsheet_token = path_parts[-1] if path_parts else None
            
            # 从查询参数中提取sheet_id
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            sheet_id = query_params.get('sheet', [None])[0]
            
            if not spreadsheet_token or not sheet_id:
                raise ValueError(f"无法解析URL: {url}")
                
            return spreadsheet_token, sheet_id
            
        except Exception as e:
            raise Exception(f"URL解析失败: {str(e)}")
    
    def get_sheet_info(self, spreadsheet_token):
        """
        获取表格基本信息和工作表列表
        """
        url = f"https://open.feishu.cn/open-apis/sheets/v1/spreadsheets/{spreadsheet_token}/sheets"
        headers = self.auth.get_headers()

        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                return result.get("data", {})
            else:
                # 如果API失败，返回空数据而不是抛出异常
                print(f"⚠️ 获取表格信息失败: {result.get('msg', '未知错误')}")
                return {}

        except requests.exceptions.RequestException as e:
            print(f"⚠️ 网络请求失败: {str(e)}")
            return {}
    
    def get_sheet_data(self, spreadsheet_token, sheet_id, range_str=""):
        """
        获取表格数据
        """
        # 构建API URL
        url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token}/values/{sheet_id}"
        if range_str:
            url += f"!{range_str}"
        
        headers = self.auth.get_headers()
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                # 从v2 API的响应结构中提取数据
                data = result.get("data", {})
                value_range = data.get("valueRange", {})
                return value_range.get("values", [])
            else:
                raise Exception(f"获取表格数据失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
    
    def process_sheet_data(self, raw_data):
        """
        处理原始表格数据，转换为结构化数据
        """
        if not raw_data:
            return {'headers': [], 'data': []}

        # 假设第一行是表头
        headers = []
        data_rows = []

        for i, row in enumerate(raw_data):
            if i == 0:
                # 处理表头
                headers = []
                for cell in row:
                    if isinstance(cell, dict):
                        # 飞书API返回的单元格格式
                        cell_text = cell.get('text', '')
                        if not cell_text and 'displayValue' in cell:
                            cell_text = cell.get('displayValue', '')
                    elif isinstance(cell, list) and len(cell) > 0:
                        # 如果是列表，取第一个元素
                        cell_text = str(cell[0]) if cell[0] is not None else ''
                    else:
                        cell_text = str(cell) if cell is not None else ''
                    headers.append(cell_text)
            else:
                # 处理数据行
                row_data = {}
                for j, cell in enumerate(row):
                    if j < len(headers):
                        if isinstance(cell, dict):
                            # 飞书API返回的单元格格式
                            cell_value = cell.get('text', '')
                            if not cell_value and 'displayValue' in cell:
                                cell_value = cell.get('displayValue', '')
                        elif isinstance(cell, list) and len(cell) > 0:
                            # 如果是列表，取第一个元素
                            cell_value = str(cell[0]) if cell[0] is not None else ''
                        else:
                            cell_value = str(cell) if cell is not None else ''

                        header_name = headers[j] if j < len(headers) else f'column_{j}'

                        # 特殊处理日期列
                        if header_name in ['Date', 'Start Date', 'End Date'] and cell_value:
                            cell_value = self.convert_excel_date(cell_value)

                        row_data[header_name] = cell_value
                data_rows.append(row_data)

        return {
            'headers': headers,
            'data': data_rows
        }
    
    def get_sheet_name_by_id(self, spreadsheet_token, sheet_id):
        """
        根据sheet_id获取真实的sheet名称
        """
        try:
            # 尝试从API获取真实的Sheet名称
            sheet_info = self.get_sheet_info(spreadsheet_token)
            sheets = sheet_info.get("sheets", [])

            for sheet in sheets:
                if sheet.get("sheet_id") == sheet_id:
                    real_name = sheet.get("title", "")
                    if real_name:
                        print(f"  📋 获取到真实Sheet名称: {real_name}")
                        return real_name

        except Exception as e:
            print(f"⚠️ 获取Sheet名称失败: {str(e)}")

        # 如果API获取失败，使用预定义的英文表名
        sheet_name_mapping = {
            "YPHiE4": "Sponsored_Products_Advertised_product_report",
            "70b4b8": "Sponsored_Products_Search_term_report",
            "56Nxuf": "Sponsored_Products_Campaigns_report",
            "bHQqAJ": "Sponsored_Products_Purchased_product_report",
            "FpR4E8": "Detail_page_Sales_and_Traffic_by_child_item"
        }

        fallback_name = sheet_name_mapping.get(sheet_id, f"sheet_{sheet_id}")
        print(f"  📋 使用预定义Sheet名称: {fallback_name}")
        return fallback_name

    def get_all_sheets_data(self):
        """
        获取所有表格的数据
        """
        all_data = {}

        for i, url in enumerate(self.sheet_urls):
            if not url.strip():
                continue

            try:
                print(f"📊 正在获取表格 {i+1} 的数据...")

                # 解析URL
                spreadsheet_token, sheet_id = self.parse_sheet_url(url.strip())

                # 获取真实的Sheet名称
                real_sheet_name = self.get_sheet_name_by_id(spreadsheet_token, sheet_id)
                print(f"  📋 Sheet名称: {real_sheet_name}")

                # 获取表格数据
                raw_data = self.get_sheet_data(spreadsheet_token, sheet_id)
                print(f"  📋 原始数据类型: {type(raw_data)}")
                if raw_data:
                    print(f"  📋 数据行数: {len(raw_data)}")
                    if len(raw_data) > 0:
                        print(f"  📋 第一行数据类型: {type(raw_data[0])}")
                        print(f"  📋 第一行数据: {raw_data[0]}")

                # 处理数据
                processed_data = self.process_sheet_data(raw_data)

                # 使用真实的Sheet名称作为key
                all_data[real_sheet_name] = {
                    'url': url.strip(),
                    'spreadsheet_token': spreadsheet_token,
                    'sheet_id': sheet_id,
                    'real_name': real_sheet_name,
                    'headers': processed_data['headers'],
                    'data': processed_data['data'],
                    'row_count': len(processed_data['data'])
                }

                print(f"✅ 表格 {i+1} ({real_sheet_name}) 数据获取成功，共 {len(processed_data['data'])} 行数据")

            except Exception as e:
                print(f"❌ 表格 {i+1} 数据获取失败: {str(e)}")
                continue

        return all_data

def test_sheets():
    """
    测试表格数据获取功能
    """
    try:
        sheets = FeishuSheets()
        data = sheets.get_all_sheets_data()
        
        print(f"\n📈 总共获取了 {len(data)} 个表格的数据:")
        for sheet_name, sheet_data in data.items():
            print(f"  - {sheet_name}: {sheet_data['row_count']} 行数据")
            print(f"    表头: {sheet_data['headers']}")
        
        return data
        
    except Exception as e:
        print(f"❌ 表格数据获取测试失败: {str(e)}")
        return None

if __name__ == "__main__":
    test_sheets()
