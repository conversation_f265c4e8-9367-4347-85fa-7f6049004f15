"""
一键清空并上传飞书数据到MySQL
"""

import sys
import time
from datetime import datetime
from feishu_auth import <PERSON>ishuAuth
from feishu_sheets import FeishuSheets
from mysql_handler import MySQLHandler

def upload_fresh_data():
    """
    一键清空并上传新数据
    """
    print("🚀 一键清空并上传飞书数据到MySQL")
    print("=" * 50)
    
    try:
        # 初始化组件
        print("📊 初始化飞书表格处理器...")
        feishu_sheets = FeishuSheets()
        
        print("🗄️ 初始化MySQL数据库连接...")
        mysql_handler = MySQLHandler()
        
        if not mysql_handler.connect():
            print("❌ MySQL数据库连接失败")
            return False
        
        print("✅ 所有组件初始化成功")
        
        # 获取飞书表格数据
        print("\n📊 正在获取飞书表格数据...")
        sheets_data = feishu_sheets.get_all_sheets_data()
        
        if not sheets_data:
            print("❌ 没有获取到任何表格数据")
            return False
        
        print(f"✅ 成功获取 {len(sheets_data)} 个表格的数据")
        
        # 处理每个表格的数据
        total_uploaded = 0
        successful_tables = 0
        
        for sheet_name, sheet_data in sheets_data.items():
            try:
                print(f"\n📋 正在处理表格: {sheet_name}")
                print(f"   - 数据行数: {sheet_data['row_count']}")
                
                # 创建数据表
                table_name, clean_headers = mysql_handler.create_table_from_headers(
                    sheet_name, 
                    sheet_data['headers']
                )
                
                # 清空现有数据
                mysql_handler.clear_table(table_name)
                
                # 插入数据
                if sheet_data['data']:
                    inserted_count = mysql_handler.insert_data(
                        table_name,
                        sheet_data['headers'],  # 原始表头
                        clean_headers,          # 清理后的表头
                        sheet_data['data']
                    )
                    total_uploaded += inserted_count
                    successful_tables += 1
                    print(f"✅ 表格 {sheet_name} 处理完成")
                else:
                    print(f"⚠️ 表格 {sheet_name} 没有数据需要上传")
                
            except Exception as e:
                print(f"❌ 处理表格 {sheet_name} 时出错: {str(e)}")
                continue
        
        # 输出总结
        print("\n" + "="*50)
        print("📊 数据上传完成总结")
        print("="*50)
        print(f"✅ 成功处理表格数量: {successful_tables}/{len(sheets_data)}")
        print(f"✅ 总共上传数据行数: {total_uploaded}")
        print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        mysql_handler.close()
        return successful_tables > 0
        
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("⚠️ 警告：此操作将清空Rich数据库中的所有现有数据！")
    confirm = input("确认继续？(输入 'yes' 确认): ").strip().lower()
    
    if confirm == 'yes':
        success = upload_fresh_data()
        if success:
            print("\n🎉 数据上传成功完成！")
        else:
            print("\n❌ 数据上传失败！")
    else:
        print("操作已取消。")
