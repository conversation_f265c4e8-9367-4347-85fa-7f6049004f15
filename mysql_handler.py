"""
MySQL数据库操作模块
用于连接MySQL数据库并进行数据操作
"""

import mysql.connector
from mysql.connector import Error
import os
import re
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class MySQLHandler:
    def __init__(self):
        self.host = os.getenv('MYSQL_HOST')
        self.port = int(os.getenv('MYSQL_PORT', 3306))
        self.user = os.getenv('MYSQL_USER')
        self.password = os.getenv('MYSQL_PASSWORD')
        self.database = os.getenv('MYSQL_DATABASE')
        self.connection = None
        
        if not all([self.host, self.user, self.password, self.database]):
            raise ValueError("请在.env文件中设置完整的MySQL连接信息")
    
    def connect(self):
        """
        连接到MySQL数据库
        """
        try:
            # 首先尝试直接连接到指定数据库
            try:
                self.connection = mysql.connector.connect(
                    host=self.host,
                    port=self.port,
                    user=self.user,
                    password=self.password,
                    database=self.database
                )

                if self.connection.is_connected():
                    print(f"✅ 数据库 '{self.database}' 连接成功")
                    return True

            except Error as e:
                if "Unknown database" in str(e):
                    print(f"⚠️ 数据库 '{self.database}' 不存在，尝试创建...")

                    # 连接到MySQL服务器（不指定数据库）
                    self.connection = mysql.connector.connect(
                        host=self.host,
                        port=self.port,
                        user=self.user,
                        password=self.password
                    )

                    if self.connection.is_connected():
                        print("✅ MySQL服务器连接成功")

                        # 尝试创建数据库
                        try:
                            self.create_database()

                            # 重新连接到指定数据库
                            self.connection.close()
                            self.connection = mysql.connector.connect(
                                host=self.host,
                                port=self.port,
                                user=self.user,
                                password=self.password,
                                database=self.database
                            )

                            print(f"✅ 数据库 '{self.database}' 创建并连接成功")
                            return True

                        except Error as create_error:
                            print(f"⚠️ 无法创建数据库 '{self.database}': {str(create_error)}")
                            print(f"ℹ️ 请手动创建数据库或联系管理员")
                            print(f"ℹ️ 假设数据库已存在，尝试直接连接...")

                            # 关闭当前连接，尝试直接连接到数据库
                            self.connection.close()
                            try:
                                self.connection = mysql.connector.connect(
                                    host=self.host,
                                    port=self.port,
                                    user=self.user,
                                    password=self.password,
                                    database=self.database
                                )
                                print(f"✅ 数据库 '{self.database}' 连接成功")
                                return True
                            except Error as final_error:
                                print(f"❌ 最终连接失败: {str(final_error)}")
                                return False
                else:
                    raise e

        except Error as e:
            print(f"❌ MySQL连接失败: {str(e)}")
            return False
    
    def create_database(self):
        """
        创建数据库（如果不存在）
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 '{self.database}' 创建成功或已存在")
            cursor.close()
        except Error as e:
            print(f"❌ 创建数据库失败: {str(e)}")
            raise
    
    def sanitize_column_name(self, name):
        """
        清理列名，确保符合MySQL命名规范
        """
        # 移除特殊字符，只保留字母、数字和下划线
        sanitized = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(name))
        # 如果以数字开头，添加前缀
        if sanitized and sanitized[0].isdigit():
            sanitized = f"col_{sanitized}"
        # 如果为空，使用默认名称
        if not sanitized:
            sanitized = "unnamed_column"
        return sanitized

    def sanitize_table_name(self, name):
        """
        清理表名，保留中文字符，确保符合MySQL命名规范
        """
        # 对于表名，我们保留更多字符，包括中文
        # 只移除一些特殊的SQL关键字符
        sanitized = re.sub(r'[`\'"\\;]', '_', str(name))
        # 移除首尾空格
        sanitized = sanitized.strip()
        # 如果为空，使用默认名称
        if not sanitized:
            sanitized = "unnamed_table"
        return sanitized
    
    def create_table_from_headers(self, table_name, headers):
        """
        根据表头创建数据表，返回(表名, 清理后的表头列表)
        """
        try:
            # 确保连接仍然有效
            if not self.connection or not self.connection.is_connected():
                print("⚠️ MySQL连接已断开，重新连接...")
                self.connect()

            cursor = self.connection.cursor()

            # 清理表名（保留中文字符）
            table_name = self.sanitize_table_name(table_name)

            # 构建CREATE TABLE语句和清理后的表头映射
            columns = []
            columns.append("id INT AUTO_INCREMENT PRIMARY KEY")

            clean_headers = []
            for header in headers:
                if header.strip():  # 跳过空表头
                    clean_header = self.sanitize_column_name(header)
                    columns.append(f"`{clean_header}` TEXT")
                    clean_headers.append(clean_header)

            # 添加创建时间和更新时间
            columns.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")

            create_sql = f"""
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                {', '.join(columns)}
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            cursor.execute(create_sql)
            self.connection.commit()
            print(f"✅ 数据表 '{table_name}' 创建成功")

            cursor.close()
            return table_name, clean_headers

        except Error as e:
            print(f"❌ 创建数据表失败: {str(e)}")
            raise
    
    def insert_data(self, table_name, original_headers, clean_headers, data_rows):
        """
        插入数据到表中
        original_headers: 原始表头列表
        clean_headers: 清理后的表头列表
        """
        try:
            if not data_rows:
                print("⚠️ 没有数据需要插入")
                return 0

            # 确保连接仍然有效
            if not self.connection or not self.connection.is_connected():
                print("⚠️ MySQL连接已断开，重新连接...")
                self.connect()

            cursor = self.connection.cursor()

            # 构建INSERT语句
            placeholders = ', '.join(['%s'] * len(clean_headers))
            columns_str = ', '.join([f"`{h}`" for h in clean_headers])

            insert_sql = f"INSERT INTO `{table_name}` ({columns_str}) VALUES ({placeholders})"

            # 准备数据 - 使用原始表头获取数据
            insert_data = []
            for row in data_rows:
                row_values = []
                for header in original_headers:
                    if header.strip():  # 只处理非空表头
                        value = row.get(header, '')
                        # 处理None值
                        if value is None:
                            value = ''
                        row_values.append(str(value))
                insert_data.append(tuple(row_values))

            # 批量插入数据
            cursor.executemany(insert_sql, insert_data)
            self.connection.commit()

            inserted_count = cursor.rowcount
            print(f"✅ 成功插入 {inserted_count} 行数据到表 '{table_name}'")

            cursor.close()
            return inserted_count

        except Error as e:
            print(f"❌ 插入数据失败: {str(e)}")
            self.connection.rollback()
            raise
    
    def clear_table(self, table_name):
        """
        清空表数据
        """
        try:
            # 确保连接仍然有效
            if not self.connection or not self.connection.is_connected():
                print("⚠️ MySQL连接已断开，重新连接...")
                self.connect()

            cursor = self.connection.cursor()
            cursor.execute(f"DELETE FROM `{table_name}`")
            self.connection.commit()
            print(f"✅ 表 '{table_name}' 数据已清空")
            cursor.close()
        except Error as e:
            print(f"❌ 清空表数据失败: {str(e)}")
            raise
    
    def close(self):
        """
        关闭数据库连接
        """
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("✅ MySQL连接已关闭")

def test_mysql():
    """
    测试MySQL连接功能
    """
    try:
        db = MySQLHandler()
        if db.connect():
            print("✅ MySQL连接测试成功")
            
            # 测试创建表
            test_headers = ['姓名', '年龄', '城市']
            table_name = db.create_table_from_headers('test_table', test_headers)
            
            # 测试插入数据
            test_data = [
                {'姓名': '张三', '年龄': '25', '城市': '北京'},
                {'姓名': '李四', '年龄': '30', '城市': '上海'}
            ]
            db.insert_data(table_name, test_headers, test_data)
            
            db.close()
            return True
        else:
            return False
    except Exception as e:
        print(f"❌ MySQL测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_mysql()
