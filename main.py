"""
飞书表格数据上传到MySQL主程序
整合所有模块，实现完整的数据获取、处理和上传流程
"""

import sys
import time
from datetime import datetime
from feishu_auth import FeishuAuth
from feishu_sheets import FeishuSheets
from mysql_handler import MySQLHandler

class DataUploader:
    def __init__(self):
        self.feishu_sheets = None
        self.mysql_handler = None
        
    def initialize(self):
        """
        初始化所有组件
        """
        try:
            print("🚀 正在初始化数据上传程序...")
            
            # 初始化飞书表格处理器
            print("📊 初始化飞书表格处理器...")
            self.feishu_sheets = FeishuSheets()
            
            # 初始化MySQL处理器
            print("🗄️ 初始化MySQL数据库连接...")
            self.mysql_handler = MySQLHandler()
            
            # 测试MySQL连接
            if not self.mysql_handler.connect():
                raise Exception("MySQL数据库连接失败")
            
            print("✅ 所有组件初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {str(e)}")
            return False
    
    def upload_data(self, clear_existing=False):
        """
        执行数据上传流程
        """
        try:
            print("\n" + "="*50)
            print("📤 开始数据上传流程")
            print("="*50)
            
            # 获取飞书表格数据
            print("\n📊 正在获取飞书表格数据...")
            sheets_data = self.feishu_sheets.get_all_sheets_data()
            
            if not sheets_data:
                print("❌ 没有获取到任何表格数据")
                return False
            
            print(f"✅ 成功获取 {len(sheets_data)} 个表格的数据")
            
            # 处理每个表格的数据
            total_uploaded = 0
            successful_tables = 0
            
            for sheet_name, sheet_data in sheets_data.items():
                try:
                    print(f"\n📋 正在处理表格: {sheet_name}")
                    print(f"   - 数据行数: {sheet_data['row_count']}")
                    print(f"   - 表头: {sheet_data['headers']}")
                    
                    # 创建数据表
                    table_name, clean_headers = self.mysql_handler.create_table_from_headers(
                        sheet_name,
                        sheet_data['headers']
                    )

                    # 如果需要清空现有数据
                    if clear_existing:
                        try:
                            self.mysql_handler.clear_table(table_name)
                        except Exception as e:
                            if "DELETE command denied" in str(e):
                                print(f"⚠️ 跳过清空表数据（用户没有DELETE权限），将追加数据到现有表中")
                            else:
                                raise e

                    # 插入数据
                    if sheet_data['data']:
                        inserted_count = self.mysql_handler.insert_data(
                            table_name,
                            sheet_data['headers'],  # 原始表头
                            clean_headers,          # 清理后的表头
                            sheet_data['data']
                        )
                        total_uploaded += inserted_count
                        successful_tables += 1
                        print(f"✅ 表格 {sheet_name} 处理完成")
                    else:
                        print(f"⚠️ 表格 {sheet_name} 没有数据需要上传")
                    
                except Exception as e:
                    print(f"❌ 处理表格 {sheet_name} 时出错: {str(e)}")
                    continue
            
            # 输出总结
            print("\n" + "="*50)
            print("📊 数据上传完成总结")
            print("="*50)
            print(f"✅ 成功处理表格数量: {successful_tables}/{len(sheets_data)}")
            print(f"✅ 总共上传数据行数: {total_uploaded}")
            print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            return successful_tables > 0
            
        except Exception as e:
            print(f"❌ 数据上传流程失败: {str(e)}")
            return False
    
    def cleanup(self):
        """
        清理资源
        """
        if self.mysql_handler:
            self.mysql_handler.close()
        print("🧹 资源清理完成")

def main():
    """
    主函数
    """
    uploader = DataUploader()
    
    try:
        # 初始化
        if not uploader.initialize():
            print("❌ 程序初始化失败，退出")
            sys.exit(1)
        
        # 询问是否清空现有数据
        print("\n❓ 是否清空数据库中的现有数据？")
        print("   输入 'y' 或 'yes' 清空现有数据")
        print("   输入其他任何内容保留现有数据并追加新数据")
        
        user_input = input("请选择: ").strip().lower()
        clear_existing = user_input in ['y', 'yes']
        
        if clear_existing:
            print("⚠️ 将清空现有数据并重新上传")
        else:
            print("ℹ️ 将保留现有数据并追加新数据")
        
        # 执行数据上传
        success = uploader.upload_data(clear_existing=clear_existing)
        
        if success:
            print("\n🎉 数据上传程序执行成功！")
            sys.exit(0)
        else:
            print("\n❌ 数据上传程序执行失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        sys.exit(1)
    finally:
        uploader.cleanup()

if __name__ == "__main__":
    main()
