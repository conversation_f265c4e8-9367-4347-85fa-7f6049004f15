"""
飞书API认证模块
用于获取access_token进行API调用
"""

import requests
import json
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class FeishuAuth:
    def __init__(self):
        self.app_id = os.getenv('FEISHU_APP_ID')
        self.app_secret = os.getenv('FEISHU_APP_SECRET')
        self.access_token = None
        
        if not self.app_id or not self.app_secret:
            raise ValueError("请在.env文件中设置FEISHU_APP_ID和FEISHU_APP_SECRET")
    
    def get_tenant_access_token(self):
        """
        获取tenant_access_token
        用于访问飞书开放平台API
        """
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        
        headers = {
            "Content-Type": "application/json; charset=utf-8"
        }
        
        data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                self.access_token = result.get("tenant_access_token")
                print("✅ 飞书API认证成功")
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")
    
    def get_headers(self):
        """
        获取带有认证信息的请求头
        """
        if not self.access_token:
            self.get_tenant_access_token()
        
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }

def test_auth():
    """
    测试认证功能
    """
    try:
        auth = FeishuAuth()
        token = auth.get_tenant_access_token()
        print(f"Access Token: {token[:20]}...")
        return True
    except Exception as e:
        print(f"❌ 认证测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_auth()
