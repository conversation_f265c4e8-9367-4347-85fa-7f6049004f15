# 飞书表格数据上传到MySQL

这个项目用于将飞书表格中的数据自动上传到MySQL数据库。

## 功能特性

- 从飞书表格API获取数据
- 自动创建MySQL数据库表结构
- 批量上传数据到MySQL数据库
- 支持多个表格同时处理
- 支持数据清空和追加模式
- 完整的错误处理和日志输出

## 项目结构

```
├── main.py              # 主程序入口
├── feishu_auth.py       # 飞书API认证模块
├── feishu_sheets.py     # 飞书表格数据获取模块
├── mysql_handler.py     # MySQL数据库操作模块
├── test_modules.py      # 模块测试脚本
├── requirements.txt     # 依赖包列表
├── .env.example        # 环境变量配置模板
├── .env                # 环境变量配置文件（需要手动创建）
└── README.md           # 项目说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置步骤

### 1. 获取飞书应用凭证

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 获取 App ID 和 App Secret
4. 在应用权限管理中添加以下权限：
   - `sheets:spreadsheet` (查看、编辑、评论和管理电子表格)

### 2. 配置环境变量

编辑 `.env` 文件，填写以下信息：

```env
# 飞书应用配置
FEISHU_APP_ID=你的飞书App_ID
FEISHU_APP_SECRET=你的飞书App_Secret

# MySQL数据库配置（已预配置）
MYSQL_HOST=*************
MYSQL_PORT=3306
MYSQL_USER=Brady
MYSQL_PASSWORD=98c06z27W@
MYSQL_DATABASE=feishu_data

# 飞书表格URL配置（已预配置）
SHEET_URLS=https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=YPHiE4,https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=70b4b8,https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=56Nxuf,https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=bHQqAJ,https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=FpR4E8
```

## 使用方法

### 1. 测试配置

首先运行测试脚本确保所有配置正确：

```bash
python test_modules.py
```

### 2. 运行主程序

```bash
python main.py
```

程序会询问是否清空现有数据：
- 输入 `y` 或 `yes`：清空数据库中的现有数据并重新上传
- 输入其他内容：保留现有数据并追加新数据

## 表格URL列表

项目配置了以下5个飞书表格：

1. https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=YPHiE4
2. https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=70b4b8
3. https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=56Nxuf
4. https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=bHQqAJ
5. https://seller-2025.feishu.cn/sheets/DTdlsDEOZh3JHGthnviciXDGnDb?sheet=FpR4E8

## 数据库表结构

程序会自动为每个表格创建对应的MySQL数据表，表结构包括：

- `id`: 自增主键
- 表格中的各列（根据表头自动创建）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 故障排除

### 常见问题

1. **飞书API认证失败**
   - 检查 App ID 和 App Secret 是否正确
   - 确认应用权限是否包含表格访问权限

2. **MySQL连接失败**
   - 检查网络连接
   - 确认数据库服务器地址、用户名和密码

3. **表格数据获取失败**
   - 确认表格URL是否正确
   - 检查表格是否有访问权限

### 日志信息

程序运行时会输出详细的日志信息，包括：
- ✅ 成功操作
- ❌ 错误信息
- ⚠️ 警告信息
- ℹ️ 提示信息

## 技术栈

- Python 3.7+
- requests: HTTP请求库
- mysql-connector-python: MySQL数据库连接器
- python-dotenv: 环境变量管理

## 注意事项

1. 确保飞书应用有足够的权限访问目标表格
2. MySQL数据库需要支持UTF-8编码
3. 建议在生产环境中使用前先在测试环境验证
4. 大量数据上传时请注意网络稳定性
