#!/bin/bash

# 飞书表格数据上传到MySQL - 快速启动脚本

echo "🚀 飞书表格数据上传到MySQL"
echo "================================"

# 检查Python环境
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 错误：未找到Python环境"
    echo "请安装Python 3.7或更高版本"
    exit 1
fi

echo "✅ 使用Python命令: $PYTHON_CMD"

# 检查依赖包
echo "📦 检查依赖包..."
$PYTHON_CMD -c "import requests, mysql.connector, dotenv" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 依赖包未安装，正在安装..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败"
        exit 1
    fi
fi

echo "✅ 依赖包检查完成"

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "⚠️ .env文件不存在，从模板创建..."
    cp .env.example .env
    echo "📝 请编辑.env文件，填写飞书App ID和App Secret"
    echo "   FEISHU_APP_ID=你的App_ID"
    echo "   FEISHU_APP_SECRET=你的App_Secret"
    echo ""
    read -p "按回车键继续..."
fi

# 运行测试
echo ""
echo "🧪 运行配置测试..."
$PYTHON_CMD test_modules.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 配置测试通过！"
    echo ""
    read -p "是否立即运行数据上传程序？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 启动数据上传程序..."
        $PYTHON_CMD main.py
    else
        echo "💡 稍后可以运行: $PYTHON_CMD main.py"
    fi
else
    echo ""
    echo "⚠️ 配置测试未完全通过"
    echo "💡 请检查配置后手动运行: $PYTHON_CMD main.py"
fi
