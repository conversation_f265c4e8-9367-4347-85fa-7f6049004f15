#!/bin/bash

# 飞书数据上传到MySQL脚本
echo "🚀 飞书数据上传到MySQL"
echo "========================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查依赖文件
if [ ! -f ".env" ]; then
    echo "❌ .env 配置文件不存在"
    exit 1
fi

echo "选择操作模式："
echo "1. 交互式上传（可选择是否清空数据）"
echo "2. 一键清空并上传新数据"
echo "3. 退出"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🔄 启动交互式上传..."
        python3 main.py
        ;;
    2)
        echo "🔄 启动一键清空并上传..."
        python3 upload_fresh_data.py
        ;;
    3)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo "✅ 操作完成"
